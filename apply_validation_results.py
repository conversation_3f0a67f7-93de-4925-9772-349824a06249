#!/usr/bin/env python3
"""
Apply the validated balance ratios from the 1,000,000 bootstrap validation
"""

from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem

def main():
    print("🔧 Applying Validated Balance Ratios...")
    print("=" * 60)

    # Create enhanced learning system instance
    enhanced_system = EnhancedAdaptiveLearningSystem()

    # First, run validation to get the latest results
    print("📊 Running validation to get latest results...")
    validation_results = enhanced_system.run_robust_balance_validation()
    
    if validation_results.get('status') == 'success' or 'context_results' in validation_results:
        print("✅ Validation completed successfully!")

        # Check if new results are better than current performance
        context_results = validation_results.get('context_results', {})
        should_apply = True

        print("\n🔍 Checking if new results improve performance...")
        for context, data in context_results.items():
            optimal_balance = data.get('optimal_balance', {})
            new_accuracy = optimal_balance.get('accuracy', 0.0)

            # You can set your minimum acceptable accuracy here
            min_acceptable_accuracy = 0.60  # 60% minimum

            if new_accuracy < min_acceptable_accuracy:
                print(f"⚠️ {context}: New accuracy {new_accuracy:.3f} below minimum {min_acceptable_accuracy:.3f}")
                should_apply = False
            else:
                print(f"✅ {context}: New accuracy {new_accuracy:.3f} meets standards")

        if should_apply:
            # Apply the validated ratios
            print("\n🔧 Applying validated balance ratios...")
            application_results = enhanced_system.apply_validated_balance_ratios(validation_results)
        else:
            print("\n❌ Skipping application - new results don't meet minimum standards")
            application_results = {'status': 'rejected', 'message': 'Results below minimum accuracy threshold'}
        
        if application_results.get('status') == 'success':
            print(f"🎉 Successfully applied {application_results['total_changes']} balance ratio changes!")
            print(f"📝 New configuration version: {application_results['new_version']}")
            
            # Show what was applied
            changes = application_results.get('changes_applied', {})
            print("\n📋 Applied Changes:")
            for context, ratio in changes.items():
                print(f"   {context}: {ratio}")
                
        elif application_results.get('status') == 'no_changes':
            print("ℹ️ No changes applied - no statistically significant improvements found")

        elif application_results.get('status') == 'rejected':
            print(f"🛡️ Changes rejected: {application_results.get('message', 'Below quality threshold')}")

        else:
            print(f"❌ Failed to apply changes: {application_results.get('message', 'Unknown error')}")
            
    else:
        print(f"❌ Validation failed: {validation_results.get('message', 'Unknown error')}")

    print("\n" + "=" * 60)
    print("✅ Application process complete!")

if __name__ == "__main__":
    main()
